/* See https://github.com/WebKit/WebKit/blob/main/Source/WebCore/css/CSSValueKeywords.in */

:root {
  --system-ui: system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  color-scheme: light dark;
  --bg-color: transparent;
  --text-color: #000000;
  --kbd-bg: #f5f5f5;
  --kbd-border: #ddd;
  --footer-text-color: #666666;
  --footer-border: rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #e4e4e4;
    --kbd-bg: #2d2d2d;
    --kbd-border: #3d3d3d;
    --footer-text-color: #999999;
    --footer-border: rgba(255, 255, 255, 0.15);
  }
}

body {
  font-family: var(--system-ui);
  width: 290px;
  padding: 15px;
  margin: 0;
  color: var(--text-color);
  background-color: var(--bg-color);
  font-size: 13px;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  transition: background-color 0.3s, color 0.3s;
  cursor: default;
  user-select: none;
  -webkit-user-select: none;
}

h1 {
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  font-weight: 600;
}

h2, legend {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

fieldset {
  border: none;
  margin: 0 0 15px 0;
  padding: 0;
}

.settings label {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 1px 0;
  margin-bottom: 8px;
}

.settings input {
  margin: 0;
}

.settings input[type="checkbox"] {
  margin: 0;
  width: 15px;
  height: 15px;
}

.settings input[type="number"] {
  width: 50px;
  text-align: center;
}

.shortcuts {
  margin: 0;
  display: grid;
  grid-template-columns: 60px 1fr;
  gap: 8px;
  align-items: center;
}

.shortcuts dt {
  margin: 0;
}

.shortcuts dd {
  margin: 0;
}

kbd {
  display: inline-block;
  padding: 3px 6px;
  border-radius: 3px;
  background: var(--kbd-bg);
  border: 1px solid var(--kbd-border);
  box-shadow: 0 1px 1px rgba(0,0,0,0.1);
  font-family: var(--system-ui);
  font-size: 13px;
  min-width: 40px;
  text-align: center;
  line-height: 1;
}

kbd.spacebar {
  font-size: 11px;
}

footer {
  font-size: 8px;
  color: var(--footer-text-color);
  text-align: center;
  margin-top: 0;
  padding-top: 10px;
  border-top: 1px solid var(--footer-border);
}
