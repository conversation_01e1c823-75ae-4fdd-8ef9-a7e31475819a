// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		B804B5DC2DA08E4B000B4088 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = B804B5DB2DA08E4B000B4088 /* AppDelegate.swift */; };
		B804B5DE2DA08E4B000B4088 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = B804B5DD2DA08E4B000B4088 /* SceneDelegate.swift */; };
		B804B5E12DA08E4B000B4088 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B804B5DF2DA08E4B000B4088 /* LaunchScreen.storyboard */; };
		B804B5E42DA08E4B000B4088 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B804B5E22DA08E4B000B4088 /* Main.storyboard */; };
		B804B5ED2DA08E4B000B4088 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = B804B5EC2DA08E4B000B4088 /* AppDelegate.swift */; };
		B804B5F02DA08E4B000B4088 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B804B5EE2DA08E4B000B4088 /* Main.storyboard */; };
		B804B5F82DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS).appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = B804B5F72DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS).appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		B804B6022DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS).appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = B804B6012DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS).appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		B804B6082DA08E4B000B4088 /* Main.html in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CB2DA08E4A000B4088 /* Main.html */; };
		B804B6092DA08E4B000B4088 /* Main.html in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CB2DA08E4A000B4088 /* Main.html */; };
		B804B60A2DA08E4B000B4088 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CD2DA08E4A000B4088 /* Icon.png */; };
		B804B60B2DA08E4B000B4088 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CD2DA08E4A000B4088 /* Icon.png */; };
		B804B60C2DA08E4B000B4088 /* Style.css in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CE2DA08E4A000B4088 /* Style.css */; };
		B804B60D2DA08E4B000B4088 /* Style.css in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CE2DA08E4A000B4088 /* Style.css */; };
		B804B60E2DA08E4B000B4088 /* Script.js in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CF2DA08E4A000B4088 /* Script.js */; };
		B804B60F2DA08E4B000B4088 /* Script.js in Resources */ = {isa = PBXBuildFile; fileRef = B804B5CF2DA08E4A000B4088 /* Script.js */; };
		B804B6102DA08E4B000B4088 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B804B5D02DA08E4A000B4088 /* ViewController.swift */; };
		B804B6112DA08E4B000B4088 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B804B5D02DA08E4A000B4088 /* ViewController.swift */; };
		B804B6122DA08E4B000B4088 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B804B5D12DA08E4B000B4088 /* Assets.xcassets */; };
		B804B6132DA08E4B000B4088 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B804B5D12DA08E4B000B4088 /* Assets.xcassets */; };
		B804B6142DA08E4B000B4088 /* SafariWebExtensionHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = B804B5D32DA08E4B000B4088 /* SafariWebExtensionHandler.swift */; };
		B804B6152DA08E4B000B4088 /* SafariWebExtensionHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = B804B5D32DA08E4B000B4088 /* SafariWebExtensionHandler.swift */; };
		B804B62F2DA08E4B000B4088 /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = B804B6272DA08E4B000B4088 /* LICENSE */; };
		B804B6312DA08E4B000B4088 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = B804B6292DA08E4B000B4088 /* README.md */; };
		B804B6322DA08E4B000B4088 /* icons in Resources */ = {isa = PBXBuildFile; fileRef = B804B62A2DA08E4B000B4088 /* icons */; };
		B804B6332DA08E4B000B4088 /* manifest.json in Resources */ = {isa = PBXBuildFile; fileRef = B804B62B2DA08E4B000B4088 /* manifest.json */; };
		B804B6352DA08E4B000B4088 /* vendor in Resources */ = {isa = PBXBuildFile; fileRef = B804B62D2DA08E4B000B4088 /* vendor */; };
		B804B6362DA08E4B000B4088 /* src in Resources */ = {isa = PBXBuildFile; fileRef = B804B62E2DA08E4B000B4088 /* src */; };
		B804B6372DA08E4B000B4088 /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = B804B6272DA08E4B000B4088 /* LICENSE */; };
		B804B6392DA08E4B000B4088 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = B804B6292DA08E4B000B4088 /* README.md */; };
		B804B63A2DA08E4B000B4088 /* icons in Resources */ = {isa = PBXBuildFile; fileRef = B804B62A2DA08E4B000B4088 /* icons */; };
		B804B63B2DA08E4B000B4088 /* manifest.json in Resources */ = {isa = PBXBuildFile; fileRef = B804B62B2DA08E4B000B4088 /* manifest.json */; };
		B804B63D2DA08E4B000B4088 /* vendor in Resources */ = {isa = PBXBuildFile; fileRef = B804B62D2DA08E4B000B4088 /* vendor */; };
		B804B63E2DA08E4B000B4088 /* src in Resources */ = {isa = PBXBuildFile; fileRef = B804B62E2DA08E4B000B4088 /* src */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B804B5F92DA08E4B000B4088 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B804B5C52DA08E4A000B4088 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B804B5F62DA08E4B000B4088;
			remoteInfo = "Amp for Bandcamp (iOS)";
		};
		B804B6032DA08E4B000B4088 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B804B5C52DA08E4A000B4088 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B804B6002DA08E4B000B4088;
			remoteInfo = "Amp for Bandcamp (macOS)";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		B804B61B2DA08E4B000B4088 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				B804B5F82DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS).appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B6222DA08E4B000B4088 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				B804B6022DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS).appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		B804B5CC2DA08E4A000B4088 /* Base */ = {isa = PBXFileReference; lastKnownFileType = text.html; name = Base; path = Base.lproj/Main.html; sourceTree = "<group>"; };
		B804B5CD2DA08E4A000B4088 /* Icon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Icon.png; sourceTree = "<group>"; };
		B804B5CE2DA08E4A000B4088 /* Style.css */ = {isa = PBXFileReference; lastKnownFileType = text.css; path = Style.css; sourceTree = "<group>"; };
		B804B5CF2DA08E4A000B4088 /* Script.js */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.javascript; path = Script.js; sourceTree = "<group>"; };
		B804B5D02DA08E4A000B4088 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		B804B5D12DA08E4B000B4088 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		B804B5D32DA08E4B000B4088 /* SafariWebExtensionHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafariWebExtensionHandler.swift; sourceTree = "<group>"; };
		B804B5D82DA08E4B000B4088 /* Amp for Bandcamp (iOS).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Amp for Bandcamp (iOS).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B804B5DB2DA08E4B000B4088 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		B804B5DD2DA08E4B000B4088 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		B804B5E02DA08E4B000B4088 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		B804B5E32DA08E4B000B4088 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		B804B5E52DA08E4B000B4088 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B804B5EA2DA08E4B000B4088 /* Amp for Bandcamp (macOS).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Amp for Bandcamp (macOS).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B804B5EC2DA08E4B000B4088 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		B804B5EF2DA08E4B000B4088 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		B804B5F12DA08E4B000B4088 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B804B5F22DA08E4B000B4088 /* Amp for Bandcamp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Amp for Bandcamp.entitlements"; sourceTree = "<group>"; };
		B804B5F72DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS).appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "Amp for Bandcamp Extension (iOS).appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		B804B5FC2DA08E4B000B4088 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B804B6012DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS).appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "Amp for Bandcamp Extension (macOS).appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		B804B6062DA08E4B000B4088 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B804B6072DA08E4B000B4088 /* Amp for Bandcamp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Amp for Bandcamp.entitlements"; sourceTree = "<group>"; };
		B804B6272DA08E4B000B4088 /* LICENSE */ = {isa = PBXFileReference; lastKnownFileType = text; name = LICENSE; path = ../../LICENSE; sourceTree = "<group>"; };
		B804B6292DA08E4B000B4088 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = README.md; path = ../../README.md; sourceTree = "<group>"; };
		B804B62A2DA08E4B000B4088 /* icons */ = {isa = PBXFileReference; lastKnownFileType = folder; name = icons; path = ../../icons; sourceTree = "<group>"; };
		B804B62B2DA08E4B000B4088 /* manifest.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; name = manifest.json; path = ../../manifest.json; sourceTree = "<group>"; };
		B804B62D2DA08E4B000B4088 /* vendor */ = {isa = PBXFileReference; lastKnownFileType = folder; name = vendor; path = ../../vendor; sourceTree = "<group>"; };
		B804B62E2DA08E4B000B4088 /* src */ = {isa = PBXFileReference; lastKnownFileType = folder; name = src; path = ../../src; sourceTree = "<group>"; };
		B804B63F2DA09000000B4088 /* Base.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Base.xcconfig; path = xcconfig/Base.xcconfig; sourceTree = "<group>"; };
		B804B6402DA09000000B4088 /* Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = xcconfig/Debug.xcconfig; sourceTree = "<group>"; };
		B804B6412DA09000000B4088 /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = xcconfig/Release.xcconfig; sourceTree = "<group>"; };
		B804B6422DA09000000B4088 /* iOS-Base.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "iOS-Base.xcconfig"; path = "xcconfig/iOS-Base.xcconfig"; sourceTree = "<group>"; };
		B804B6432DA09000000B4088 /* iOS-Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "iOS-Debug.xcconfig"; path = "xcconfig/iOS-Debug.xcconfig"; sourceTree = "<group>"; };
		B804B6442DA09000000B4088 /* iOS-Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "iOS-Release.xcconfig"; path = "xcconfig/iOS-Release.xcconfig"; sourceTree = "<group>"; };
		B804B6452DA09000000B4088 /* Mac-Base.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Mac-Base.xcconfig"; path = "xcconfig/Mac-Base.xcconfig"; sourceTree = "<group>"; };
		B804B6462DA09000000B4088 /* Mac-Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Mac-Debug.xcconfig"; path = "xcconfig/Mac-Debug.xcconfig"; sourceTree = "<group>"; };
		B804B6472DA09000000B4088 /* Mac-Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Mac-Release.xcconfig"; path = "xcconfig/Mac-Release.xcconfig"; sourceTree = "<group>"; };
		B804B6482DA09000000B4088 /* Ext-iOS-Base.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Ext-iOS-Base.xcconfig"; path = "xcconfig/Ext-iOS-Base.xcconfig"; sourceTree = "<group>"; };
		B804B6492DA09000000B4088 /* Ext-iOS-Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Ext-iOS-Debug.xcconfig"; path = "xcconfig/Ext-iOS-Debug.xcconfig"; sourceTree = "<group>"; };
		B804B64A2DA09000000B4088 /* Ext-iOS-Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Ext-iOS-Release.xcconfig"; path = "xcconfig/Ext-iOS-Release.xcconfig"; sourceTree = "<group>"; };
		B804B64B2DA09000000B4088 /* Ext-Mac-Base.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Ext-Mac-Base.xcconfig"; path = "xcconfig/Ext-Mac-Base.xcconfig"; sourceTree = "<group>"; };
		B804B64C2DA09000000B4088 /* Ext-Mac-Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Ext-Mac-Debug.xcconfig"; path = "xcconfig/Ext-Mac-Debug.xcconfig"; sourceTree = "<group>"; };
		B804B64D2DA09000000B4088 /* Ext-Mac-Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = "Ext-Mac-Release.xcconfig"; path = "xcconfig/Ext-Mac-Release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B804B5D52DA08E4B000B4088 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5E72DA08E4B000B4088 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5F42DA08E4B000B4088 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5FE2DA08E4B000B4088 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B804B5C42DA08E4A000B4088 = {
			isa = PBXGroup;
			children = (
				B804B5DA2DA08E4B000B4088 /* App-iOS */,
				B804B5EB2DA08E4B000B4088 /* App-Mac */,
				B804B5C92DA08E4A000B4088 /* App-Shared */,
				B804B5FB2DA08E4B000B4088 /* Ext-iOS */,
				B804B6052DA08E4B000B4088 /* Ext-Mac */,
				B804B5D22DA08E4B000B4088 /* Ext-Shared */,
				B804B64E2DA09000000B4088 /* xcconfig */,
				B804B5D92DA08E4B000B4088 /* Products */,
			);
			sourceTree = "<group>";
		};
		B804B5C92DA08E4A000B4088 /* App-Shared */ = {
			isa = PBXGroup;
			children = (
				B804B5D02DA08E4A000B4088 /* ViewController.swift */,
				B804B5D12DA08E4B000B4088 /* Assets.xcassets */,
				B804B5CA2DA08E4A000B4088 /* Resources */,
			);
			path = "App-Shared";
			sourceTree = "<group>";
		};
		B804B5CA2DA08E4A000B4088 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B804B5CB2DA08E4A000B4088 /* Main.html */,
				B804B5CD2DA08E4A000B4088 /* Icon.png */,
				B804B5CE2DA08E4A000B4088 /* Style.css */,
				B804B5CF2DA08E4A000B4088 /* Script.js */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		B804B5D22DA08E4B000B4088 /* Ext-Shared */ = {
			isa = PBXGroup;
			children = (
				B804B6262DA08E4B000B4088 /* Resources */,
				B804B5D32DA08E4B000B4088 /* SafariWebExtensionHandler.swift */,
			);
			path = "Ext-Shared";
			sourceTree = "<group>";
		};
		B804B5D92DA08E4B000B4088 /* Products */ = {
			isa = PBXGroup;
			children = (
				B804B5D82DA08E4B000B4088 /* Amp for Bandcamp (iOS).app */,
				B804B5EA2DA08E4B000B4088 /* Amp for Bandcamp (macOS).app */,
				B804B5F72DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS).appex */,
				B804B6012DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS).appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B804B5DA2DA08E4B000B4088 /* App-iOS */ = {
			isa = PBXGroup;
			children = (
				B804B5DB2DA08E4B000B4088 /* AppDelegate.swift */,
				B804B5DD2DA08E4B000B4088 /* SceneDelegate.swift */,
				B804B5DF2DA08E4B000B4088 /* LaunchScreen.storyboard */,
				B804B5E22DA08E4B000B4088 /* Main.storyboard */,
				B804B5E52DA08E4B000B4088 /* Info.plist */,
			);
			path = "App-iOS";
			sourceTree = "<group>";
		};
		B804B5EB2DA08E4B000B4088 /* App-Mac */ = {
			isa = PBXGroup;
			children = (
				B804B5EC2DA08E4B000B4088 /* AppDelegate.swift */,
				B804B5EE2DA08E4B000B4088 /* Main.storyboard */,
				B804B5F12DA08E4B000B4088 /* Info.plist */,
				B804B5F22DA08E4B000B4088 /* Amp for Bandcamp.entitlements */,
			);
			path = "App-Mac";
			sourceTree = "<group>";
		};
		B804B5FB2DA08E4B000B4088 /* Ext-iOS */ = {
			isa = PBXGroup;
			children = (
				B804B5FC2DA08E4B000B4088 /* Info.plist */,
			);
			path = "Ext-iOS";
			sourceTree = "<group>";
		};
		B804B6052DA08E4B000B4088 /* Ext-Mac */ = {
			isa = PBXGroup;
			children = (
				B804B6062DA08E4B000B4088 /* Info.plist */,
				B804B6072DA08E4B000B4088 /* Amp for Bandcamp.entitlements */,
			);
			path = "Ext-Mac";
			sourceTree = "<group>";
		};
		B804B6262DA08E4B000B4088 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B804B6272DA08E4B000B4088 /* LICENSE */,
				B804B6292DA08E4B000B4088 /* README.md */,
				B804B62A2DA08E4B000B4088 /* icons */,
				B804B62B2DA08E4B000B4088 /* manifest.json */,
				B804B62D2DA08E4B000B4088 /* vendor */,
				B804B62E2DA08E4B000B4088 /* src */,
			);
			name = Resources;
			path = "Shared (Extension)";
			sourceTree = SOURCE_ROOT;
		};
		B804B64E2DA09000000B4088 /* xcconfig */ = {
			isa = PBXGroup;
			children = (
				B804B63F2DA09000000B4088 /* Base.xcconfig */,
				B804B6402DA09000000B4088 /* Debug.xcconfig */,
				B804B6412DA09000000B4088 /* Release.xcconfig */,
				B804B6422DA09000000B4088 /* iOS-Base.xcconfig */,
				B804B6432DA09000000B4088 /* iOS-Debug.xcconfig */,
				B804B6442DA09000000B4088 /* iOS-Release.xcconfig */,
				B804B6452DA09000000B4088 /* Mac-Base.xcconfig */,
				B804B6462DA09000000B4088 /* Mac-Debug.xcconfig */,
				B804B6472DA09000000B4088 /* Mac-Release.xcconfig */,
				B804B6482DA09000000B4088 /* Ext-iOS-Base.xcconfig */,
				B804B6492DA09000000B4088 /* Ext-iOS-Debug.xcconfig */,
				B804B64A2DA09000000B4088 /* Ext-iOS-Release.xcconfig */,
				B804B64B2DA09000000B4088 /* Ext-Mac-Base.xcconfig */,
				B804B64C2DA09000000B4088 /* Ext-Mac-Debug.xcconfig */,
				B804B64D2DA09000000B4088 /* Ext-Mac-Release.xcconfig */,
			);
			name = xcconfig;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B804B5D72DA08E4B000B4088 /* Amp for Bandcamp (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B804B61C2DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp (iOS)" */;
			buildPhases = (
				B804B5D42DA08E4B000B4088 /* Sources */,
				B804B5D52DA08E4B000B4088 /* Frameworks */,
				B804B5D62DA08E4B000B4088 /* Resources */,
				B804B61B2DA08E4B000B4088 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				B804B5FA2DA08E4B000B4088 /* PBXTargetDependency */,
			);
			name = "Amp for Bandcamp (iOS)";
			packageProductDependencies = (
			);
			productName = "Amp for Bandcamp (iOS)";
			productReference = B804B5D82DA08E4B000B4088 /* Amp for Bandcamp (iOS).app */;
			productType = "com.apple.product-type.application";
		};
		B804B5E92DA08E4B000B4088 /* Amp for Bandcamp (macOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B804B6232DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp (macOS)" */;
			buildPhases = (
				B804B5E62DA08E4B000B4088 /* Sources */,
				B804B5E72DA08E4B000B4088 /* Frameworks */,
				B804B5E82DA08E4B000B4088 /* Resources */,
				B804B6222DA08E4B000B4088 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				B804B6042DA08E4B000B4088 /* PBXTargetDependency */,
			);
			name = "Amp for Bandcamp (macOS)";
			packageProductDependencies = (
			);
			productName = "Amp for Bandcamp (macOS)";
			productReference = B804B5EA2DA08E4B000B4088 /* Amp for Bandcamp (macOS).app */;
			productType = "com.apple.product-type.application";
		};
		B804B5F62DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B804B6182DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp Extension (iOS)" */;
			buildPhases = (
				B804B5F32DA08E4B000B4088 /* Sources */,
				B804B5F42DA08E4B000B4088 /* Frameworks */,
				B804B5F52DA08E4B000B4088 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Amp for Bandcamp Extension (iOS)";
			packageProductDependencies = (
			);
			productName = "Amp for Bandcamp (iOS)";
			productReference = B804B5F72DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS).appex */;
			productType = "com.apple.product-type.app-extension";
		};
		B804B6002DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B804B61F2DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp Extension (macOS)" */;
			buildPhases = (
				B804B5FD2DA08E4B000B4088 /* Sources */,
				B804B5FE2DA08E4B000B4088 /* Frameworks */,
				B804B5FF2DA08E4B000B4088 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Amp for Bandcamp Extension (macOS)";
			packageProductDependencies = (
			);
			productName = "Amp for Bandcamp (macOS)";
			productReference = B804B6012DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS).appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B804B5C52DA08E4A000B4088 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					B804B5D72DA08E4B000B4088 = {
						CreatedOnToolsVersion = 16.2;
					};
					B804B5E92DA08E4B000B4088 = {
						CreatedOnToolsVersion = 16.2;
					};
					B804B5F62DA08E4B000B4088 = {
						CreatedOnToolsVersion = 16.2;
					};
					B804B6002DA08E4B000B4088 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = B804B5C82DA08E4A000B4088 /* Build configuration list for PBXProject "Amp for Bandcamp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B804B5C42DA08E4A000B4088;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = B804B5D92DA08E4B000B4088 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B804B5D72DA08E4B000B4088 /* Amp for Bandcamp (iOS) */,
				B804B5E92DA08E4B000B4088 /* Amp for Bandcamp (macOS) */,
				B804B5F62DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS) */,
				B804B6002DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS) */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B804B5D62DA08E4B000B4088 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B60A2DA08E4B000B4088 /* Icon.png in Resources */,
				B804B5E12DA08E4B000B4088 /* LaunchScreen.storyboard in Resources */,
				B804B6082DA08E4B000B4088 /* Main.html in Resources */,
				B804B60E2DA08E4B000B4088 /* Script.js in Resources */,
				B804B6122DA08E4B000B4088 /* Assets.xcassets in Resources */,
				B804B5E42DA08E4B000B4088 /* Main.storyboard in Resources */,
				B804B60C2DA08E4B000B4088 /* Style.css in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5E82DA08E4B000B4088 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B60B2DA08E4B000B4088 /* Icon.png in Resources */,
				B804B60D2DA08E4B000B4088 /* Style.css in Resources */,
				B804B5F02DA08E4B000B4088 /* Main.storyboard in Resources */,
				B804B60F2DA08E4B000B4088 /* Script.js in Resources */,
				B804B6132DA08E4B000B4088 /* Assets.xcassets in Resources */,
				B804B6092DA08E4B000B4088 /* Main.html in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5F52DA08E4B000B4088 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B6392DA08E4B000B4088 /* README.md in Resources */,
				B804B63D2DA08E4B000B4088 /* vendor in Resources */,
				B804B63A2DA08E4B000B4088 /* icons in Resources */,
				B804B63E2DA08E4B000B4088 /* src in Resources */,
				B804B63B2DA08E4B000B4088 /* manifest.json in Resources */,
				B804B6372DA08E4B000B4088 /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5FF2DA08E4B000B4088 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B6312DA08E4B000B4088 /* README.md in Resources */,
				B804B6352DA08E4B000B4088 /* vendor in Resources */,
				B804B6322DA08E4B000B4088 /* icons in Resources */,
				B804B6362DA08E4B000B4088 /* src in Resources */,
				B804B6332DA08E4B000B4088 /* manifest.json in Resources */,
				B804B62F2DA08E4B000B4088 /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B804B5D42DA08E4B000B4088 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B6102DA08E4B000B4088 /* ViewController.swift in Sources */,
				B804B5DC2DA08E4B000B4088 /* AppDelegate.swift in Sources */,
				B804B5DE2DA08E4B000B4088 /* SceneDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5E62DA08E4B000B4088 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B6112DA08E4B000B4088 /* ViewController.swift in Sources */,
				B804B5ED2DA08E4B000B4088 /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5F32DA08E4B000B4088 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B6142DA08E4B000B4088 /* SafariWebExtensionHandler.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B804B5FD2DA08E4B000B4088 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B804B6152DA08E4B000B4088 /* SafariWebExtensionHandler.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B804B5FA2DA08E4B000B4088 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B804B5F62DA08E4B000B4088 /* Amp for Bandcamp Extension (iOS) */;
			targetProxy = B804B5F92DA08E4B000B4088 /* PBXContainerItemProxy */;
		};
		B804B6042DA08E4B000B4088 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B804B6002DA08E4B000B4088 /* Amp for Bandcamp Extension (macOS) */;
			targetProxy = B804B6032DA08E4B000B4088 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		B804B5CB2DA08E4A000B4088 /* Main.html */ = {
			isa = PBXVariantGroup;
			children = (
				B804B5CC2DA08E4A000B4088 /* Base */,
			);
			name = Main.html;
			sourceTree = "<group>";
		};
		B804B5DF2DA08E4B000B4088 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				B804B5E02DA08E4B000B4088 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		B804B5E22DA08E4B000B4088 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				B804B5E32DA08E4B000B4088 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		B804B5EE2DA08E4B000B4088 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				B804B5EF2DA08E4B000B4088 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		B804B6162DA08E4B000B4088 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B6402DA09000000B4088 /* Debug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		B804B6172DA08E4B000B4088 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B6412DA09000000B4088 /* Release.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
		B804B6192DA08E4B000B4088 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B6492DA09000000B4088 /* Ext-iOS-Debug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		B804B61A2DA08E4B000B4088 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B64A2DA09000000B4088 /* Ext-iOS-Release.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
		B804B61D2DA08E4B000B4088 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B6432DA09000000B4088 /* iOS-Debug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		B804B61E2DA08E4B000B4088 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B6442DA09000000B4088 /* iOS-Release.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
		B804B6202DA08E4B000B4088 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B64C2DA09000000B4088 /* Ext-Mac-Debug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		B804B6212DA08E4B000B4088 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B64D2DA09000000B4088 /* Ext-Mac-Release.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
		B804B6242DA08E4B000B4088 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B6462DA09000000B4088 /* Mac-Debug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		B804B6252DA08E4B000B4088 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B804B6472DA09000000B4088 /* Mac-Release.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B804B5C82DA08E4A000B4088 /* Build configuration list for PBXProject "Amp for Bandcamp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B804B6162DA08E4B000B4088 /* Debug */,
				B804B6172DA08E4B000B4088 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B804B6182DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp Extension (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B804B6192DA08E4B000B4088 /* Debug */,
				B804B61A2DA08E4B000B4088 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B804B61C2DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B804B61D2DA08E4B000B4088 /* Debug */,
				B804B61E2DA08E4B000B4088 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B804B61F2DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp Extension (macOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B804B6202DA08E4B000B4088 /* Debug */,
				B804B6212DA08E4B000B4088 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B804B6232DA08E4B000B4088 /* Build configuration list for PBXNativeTarget "Amp for Bandcamp (macOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B804B6242DA08E4B000B4088 /* Debug */,
				B804B6252DA08E4B000B4088 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B804B5C52DA08E4A000B4088 /* Project object */;
}
