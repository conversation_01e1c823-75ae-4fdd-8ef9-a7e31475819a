//
//  AppDelegate.swift
//  macOS (App)
//
//  Created by <PERSON><PERSON><PERSON> on 05/04/2025.
//

import Cocoa

@main
class AppDelegate: NSObject, NSApplicationDelegate {

    func applicationDidFinishLaunching(_ notification: Notification) {
        // Override point for customization after application launch.
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> <PERSON><PERSON> {
        return true
    }

}
