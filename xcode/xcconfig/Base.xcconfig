// Base.xcconfig
// Common settings for all configurations

// Standard product name for release build
BASE_PRODUCT_NAME = Amp for Bandcamp
PRODUCT_NAME = $(BASE_PRODUCT_NAME)
INFOPLIST_KEY_CFBundleDisplayName = "Amp for Bandcamp"
INFOPLIST_KEY_LSApplicationCategoryType = public.app-category.music

// Module name configuration
BASE_MODULE_NAME = AmpForBandcamp

// Bundle identifier configuration
PRODUCT_BUNDLE_IDENTIFIER_BASE = com.enden.amp-for-bandcamp
EXTENSION_BUNDLE_ID_SUFFIX = Extension

// Versioning
MARKETING_VERSION = 1.0.2
CURRENT_PROJECT_VERSION = 1

// Code signing
CODE_SIGN_IDENTITY = Apple Development
CODE_SIGN_STYLE = Automatic
DEVELOPMENT_TEAM = 22K4Y64ED2

// Swift version
SWIFT_VERSION = 5.0

// Build settings
ENABLE_BITCODE = NO
IPHONEOS_DEPLOYMENT_TARGET = 15.0
MACOSX_DEPLOYMENT_TARGET = 13.0
GCC_TREAT_WARNINGS_AS_ERRORS = YES
SWIFT_TREAT_WARNINGS_AS_ERRORS = YES

// Asset catalogs
ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon
ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor

// Common Xcode settings
ALWAYS_SEARCH_USER_PATHS = NO
CLANG_ANALYZER_NONNULL = YES
CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE
CLANG_CXX_LANGUAGE_STANDARD = "gnu++20"
CLANG_ENABLE_MODULES = YES
CLANG_ENABLE_OBJC_ARC = YES
CLANG_ENABLE_OBJC_WEAK = YES
ENABLE_STRICT_OBJC_MSGSEND = YES
GCC_C_LANGUAGE_STANDARD = gnu17
GCC_NO_COMMON_BLOCKS = YES
GENERATE_INFOPLIST_FILE = YES

// Include license acknowledgements in the app
COPY_PHASE_STRIP = NO 
