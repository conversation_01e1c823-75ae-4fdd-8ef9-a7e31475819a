{"name": "amp-for-bandcamp", "version": "1.0.0", "description": "Amplifies Bandcamp functionality with playback controls and UI improvements", "keywords": ["bandcamp", "music", "browser-extension", "safari-extension", "chrome-extension", "edge-extension", "firefox-addon"], "author": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/rafenden/amp-for-bandcamp.git"}, "scripts": {"build:zip": "zip -r dist/amp-for-bandcamp.zip manifest.json src/ vendor/ icons/", "build:safari": "xcodebuild -project \"xcode/Amp for Bandcamp.xcodeproj\" -scheme \"Amp for Bandcamp (macOS)\" -configuration Release -derivedDataPath dist/safari clean build", "build": "npm run build:zip && npm run build:safari"}}