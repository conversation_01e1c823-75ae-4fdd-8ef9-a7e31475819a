{"name": "amp-for-bandcamp", "version": "1.0.2", "type": "module", "description": "Amplifies Bandcamp functionality with playback controls and UI improvements", "keywords": ["bandcamp", "music", "browser-extension", "safari-extension", "chrome-extension", "opera-extension", "edge-extension", "firefox-addon"], "author": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/rafenden/amp-for-bandcamp.git"}, "scripts": {"build": "npm run build:zip && npm run build:safari", "build:zip": "zip -r dist/amp-for-bandcamp.zip manifest.json src/ vendor/ icons/", "build:safari": "xcodebuild -project 'xcode/Amp for Bandcamp.xcodeproj' -scheme 'Amp for Bandcamp (macOS)' -configuration Release -derivedDataPath dist/safari clean build", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug"}, "devDependencies": {"@playwright/test": "^1.52.0", "prettier": "^3.6.2"}}